/**
 * Test script để verify logic download status
 * Kiểm tra:
 * 1. Tạo session chỉ với files chưa download
 * 2. Cập nhật status khi download thành công
 * 3. Session tiếp theo chỉ lấy files còn lại
 */

import { SupabaseClient } from './src/database/supabase.js';
import { FileDownloadService } from './src/services/file-download-service.js';

const supabase = new SupabaseClient();
const downloadService = new FileDownloadService();

async function testDownloadStatusLogic() {
    console.log('🧪 Testing download status logic...\n');

    try {
        // 1. <PERSON><PERSON>m tra có files nào trong database không
        console.log('1. Checking existing files...');
        const { data: allFiles, error: allFilesError } = await supabase.getServiceClient()
            .from('scanned_files')
            .select('id, name, user_email, download_status')
            .limit(10);

        if (allFilesError) {
            throw new Error(`Failed to get files: ${allFilesError.message}`);
        }

        console.log(`   Found ${allFiles.length} files in database`);
        allFiles.forEach(file => {
            console.log(`   - ${file.name} (${file.user_email}) - Status: ${file.download_status || 'null'}`);
        });

        // 2. Kiểm tra logic filter files chưa download
        console.log('\n2. Testing undownloaded files filter...');
        const { data: undownloadedFiles, error: undownloadedError } = await supabase.getServiceClient()
            .from('scanned_files')
            .select('id, name, user_email, download_status')
            .or('download_status.is.null,download_status.eq.not_downloaded')
            .limit(5);

        if (undownloadedError) {
            throw new Error(`Failed to get undownloaded files: ${undownloadedError.message}`);
        }

        console.log(`   Found ${undownloadedFiles.length} undownloaded files:`);
        undownloadedFiles.forEach(file => {
            console.log(`   - ${file.name} (${file.user_email}) - Status: ${file.download_status || 'null'}`);
        });

        // 3. Kiểm tra logic filter files đã download
        console.log('\n3. Testing downloaded files filter...');
        const { data: downloadedFiles, error: downloadedError } = await supabase.getServiceClient()
            .from('scanned_files')
            .select('id, name, user_email, download_status')
            .eq('download_status', 'downloaded')
            .limit(5);

        if (downloadedError) {
            throw new Error(`Failed to get downloaded files: ${downloadedError.message}`);
        }

        console.log(`   Found ${downloadedFiles.length} downloaded files:`);
        downloadedFiles.forEach(file => {
            console.log(`   - ${file.name} (${file.user_email}) - Status: ${file.download_status}`);
        });

        // 4. Test tạo session với users có files chưa download
        console.log('\n4. Testing session creation with undownloaded files...');
        
        // Lấy danh sách users có files chưa download
        const { data: usersWithUndownloaded, error: usersError } = await supabase.getServiceClient()
            .from('scanned_files')
            .select('user_email')
            .or('download_status.is.null,download_status.eq.not_downloaded')
            .limit(3);

        if (usersError) {
            throw new Error(`Failed to get users: ${usersError.message}`);
        }

        const uniqueUsers = [...new Set(usersWithUndownloaded.map(f => f.user_email))];
        console.log(`   Users with undownloaded files: ${uniqueUsers.join(', ')}`);

        if (uniqueUsers.length > 0) {
            // Simulate tạo session (không thực sự tạo)
            console.log(`   Simulating session creation for users: ${uniqueUsers.slice(0, 2).join(', ')}`);
            
            // Test calculateSessionStats logic
            const testUsers = uniqueUsers.slice(0, 2);
            const { data: sessionFiles, error: sessionError } = await supabase.getServiceClient()
                .from('scanned_files')
                .select('id, file_id, name, size, mime_type, full_path, user_email, download_status')
                .in('user_email', testUsers)
                .or('download_status.is.null,download_status.eq.not_downloaded');

            if (sessionError) {
                throw new Error(`Failed to get session files: ${sessionError.message}`);
            }

            console.log(`   Would create session with ${sessionFiles.length} files:`);
            const userFileCounts = {};
            sessionFiles.forEach(file => {
                userFileCounts[file.user_email] = (userFileCounts[file.user_email] || 0) + 1;
            });
            
            Object.entries(userFileCounts).forEach(([email, count]) => {
                console.log(`     - ${email}: ${count} files`);
            });
        }

        // 5. Test API endpoint cho undownloaded files
        console.log('\n5. Testing API logic...');
        if (uniqueUsers.length > 0) {
            const testUser = uniqueUsers[0];
            console.log(`   Testing API for user: ${testUser}`);
            
            const { data: apiFiles, error: apiError } = await supabase.getServiceClient()
                .from('scanned_files')
                .select('id, file_id, name, size, mime_type, full_path, user_email, download_status')
                .eq('user_email', testUser)
                .or('download_status.is.null,download_status.eq.not_downloaded')
                .order('full_path');

            if (apiError) {
                throw new Error(`Failed to get API files: ${apiError.message}`);
            }

            console.log(`   API would return ${apiFiles.length} undownloaded files for ${testUser}`);
        }

        console.log('\n✅ All tests completed successfully!');
        console.log('\n📋 Summary:');
        console.log(`   - Total files in database: ${allFiles.length}`);
        console.log(`   - Undownloaded files: ${undownloadedFiles.length}`);
        console.log(`   - Downloaded files: ${downloadedFiles.length}`);
        console.log(`   - Users with undownloaded files: ${uniqueUsers.length}`);

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        process.exit(1);
    }
}

// Run test
testDownloadStatusLogic();
